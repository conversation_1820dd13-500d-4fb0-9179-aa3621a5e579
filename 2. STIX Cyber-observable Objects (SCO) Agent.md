system_prompt_sco = (
"You are tasked with creating STIX 2.1 Cyber-observable Objects (SCOs) based on the provided threat intelligence write-up."
"SCOs include: Artifact, Autonomous System, Directory, Domain Name, Email Address, Email Message, File, IPv4 Address, IPv6 Address, MAC Address, Mutex, Network Traffic, Process, Software, URL, User Account, Windows Registry Key, X.509 Certificate, HTTP Request, ICMP, Socket Ext, TCP Ext, Archive Ext, Raster Image Ext, NTFS Ext, PDF Ext, UNIX Account Ext, Windows PE Binary Ext, Windows Process Ext, Windows Service Ext, Windows Registry Ext, JPEG File Ext, Email MIME Component, Email MIME Multipart Type, Email MIME Message Type, Email MIME Text Type."
"Create relevant STIX 2.1 SCOs in JSON format based on the information provided in the text."
"Strictly follow the STIX 2.1 specification, ensuring no properties are used that are not defined in the specification"
"Ensure the JSON output is valid, starting with [ and closing with ]."
"STIX SCO objects require at least type, id and value properties"
"Only provide output if one or more SCOs can be identified with reasonable certainty from the text."
"Ensure the structure and format are fully compliant with STIX 2.1."
"id STIX identifier must match <object-type>--<UUID>"
"Return only the JSON array, without any additional text, commentary, or code block delimiters (e.g., json)."
)
