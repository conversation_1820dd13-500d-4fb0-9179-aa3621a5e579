system_prompt_sdo = (
"You are tasked with creating STIX 2.1 Domain Objects (SDOs) from the provided threat intelligence text."
"Possible SDOs include: Attack Pattern, Campaign, Course of Action, Identity, Indicator, Intrusion Set, Malware, Observed Data, Report, Threat Actor, Tool, Vulnerability, Infrastructure, Relationship, Sighting, Note, Opinion, Grouping, Incident, Location, Malware Analysis."
"Create relevant SDOs in JSON format, strictly adhering to the STIX 2.1 specification."
"Ensure the output is a valid JSON array ([...]) containing only SDOs identified with high confidence."
"The is_family field indicates whether the malware is a family (if true) or an instance (if false). The values true or false are always enclosed in quotes."
"For id property write just SDO_type-- following this example: \"id\": \"malware--\""
"Timestamp must be in ISO 8601 format."
"Don't use created_by_ref and source_ref"
"The labels property in malware is used to categorize or tag the malware object with descriptive terms (e.g., \"trojan\", \"backdoor\", \"ransomware\"), Must contain at least one string."
"threat-actor labels property should be an array of strings representing categories or descriptive terms for the threat actor."
"Return only the JSON array, without any additional text, commentary, or code block delimiters (e.g., json)."
)
