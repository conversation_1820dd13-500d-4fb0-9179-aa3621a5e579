```json
{
  "name": "Operation Digital Eye - TTPs",
  "versions": {
    "attack": "14",
    "navigator": "4.9.1",
    "layer": "4.5"
  },
  "domain": "enterprise-attack",
  "description": "Key techniques used in the Operation Digital Eye campaign",
  "filters": {
    "platforms": [
      "Windows"
    ]
  },
  "sorting": 0,
  "layout": {
    "layout": "side",
    "aggregateFunction": "average",
    "showID": false,
    "showName": true,
    "showAggregateScores": false,
    "countUnscored": false,
    "expandedSubtechniques": "none"
  },
  "hideDisabled": false,
  "techniques": [
    {
      "techniqueID": "T1505.003",
      "tactic": "initial-access",
      "color": "",
      "comment": "The attackers used SQL injection as an initial access vector to infiltrate Internet-facing web and database servers.",
      "enabled": true,
      "metadata": [],
      "links": [],
      "showSubtechniques": true
    },
    {
      "techniqueID": "T1505.003",
      "tactic": "persistence",
      "color": "",
      "comment": "Deployed PHP-based webshell ‘PHPsert’ to maintain persistent access.",
      "enabled": true,
      "metadata": [],
      "links": [],
      "showSubtechniques": true
    },
    {
      "techniqueID": "T1136.001",
      "tactic": "persistence",
      "color": "",
      "comment": "Threat actors deployed authorized_keys files containing public keys for SSH access.",
      "enabled": true,
      "metadata": [],
      "links": [],
      "showSubtechniques": false
    },
    {
      "techniqueID": "T1021.001",
      "tactic": "lateral-movement",
      "color": "",
      "comment": "Used RDP connections to move laterally across the network.",
      "enabled": true,
      "metadata": [],
      "links": [],
      "showSubtechniques": false
    },
    {
      "techniqueID": "T1550.002",
      "tactic": "credential-access",
      "color": "",
      "comment": "Utilized a custom modified version of Mimikatz for pass-the-hash attacks.",
      "enabled": true,
      "metadata": [],
      "links": [],
      "showSubtechniques": false
    },
    {
      "techniqueID": "T1550.002",
      "tactic": "lateral-movement",
      "color": "",
      "comment": "Utilized a custom modified version of Mimikatz for pass-the-hash attacks.",
      "enabled": true,
      "metadata": [],
      "links": [],
      "showSubtechniques": false
    },
    {
      "techniqueID": "T1059.003",
      "tactic": "execution",
      "color": "",
      "comment": "Used various tools and script commands like ping, GetUserInfo, and CreateDump.",
      "enabled": true,
      "metadata": [],
      "links": [],
      "showSubtechniques": false
    },
    {
      "techniqueID": "T1219",
      "tactic": "command-and-control",
      "color": "",
      "comment": "Abused Visual Studio Code Remote Tunnels to maintain persistent backdoor access.",
      "enabled": true,
      "metadata": [],
      "links": [],
      "showSubtechniques": false
    },
    {
      "techniqueID": "T1027",
      "tactic": "defense-evasion",
      "color": "",
      "comment": "Used code obfuscation in webshells and tools to evade detection.",
      "enabled": true,
      "metadata": [],
      "links": [],
      "showSubtechniques": false
    },
    {
      "techniqueID": "T1003.001",
      "tactic": "credential-access",
      "color": "",
      "comment": "Used CreateDump tool to extract memory allocated to LSASS to exfiltrate credentials.",
      "enabled": true,
      "metadata": [],
      "links": [],
      "showSubtechniques": true
    },
    {
      "techniqueID": "T1105",
      "tactic": "lateral-movement",
      "color": "",
      "comment": "Downloading tools and transferring files using the `do.*` filename pattern.",
      "enabled": true,
      "metadata": [],
      "links": [],
      "showSubtechniques": false
    },
    {
      "techniqueID": "T1105",
      "tactic": "command-and-control",
      "color": "",
      "comment": "Downloading tools and transferring files using the `do.*` filename pattern.",
      "enabled": true,
      "metadata": [],
      "links": [],
      "showSubtechniques": false
    }
  ],
  "gradient": {
    "colors": ["#ff6666", "#ffe766", "#8ec843"],
    "minValue": 0,
    "maxValue": 100
  },
  "legendItems": [],
  "metadata": [],
  "links": [],
  "showTacticRowBackground": false,
  "tacticRowBackground": "#dddddd"
}
```